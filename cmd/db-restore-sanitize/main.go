package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsconfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/mneme/db-tools/pkg/config"
	"github.com/mneme/db-tools/pkg/db"
	"github.com/mneme/db-tools/pkg/k8s"
	mnemeS3 "github.com/mneme/db-tools/pkg/s3"
	"github.com/mneme/db-tools/sanitize"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

const (
	awsRegion = "ca-central-1"
)

var (
	awsConfig  aws.Config
	smClient   *secretsmanager.Client
	s3Client   *s3.Client
	k8sClient  *kubernetes.Clientset
	localMode  bool
	singleMode bool
	logLevel   string
)

func init() {
	// Define flags
	flag.BoolVar(&localMode, "local", false, "Run in local mode (no Kubernetes)")
	flag.BoolVar(&singleMode, "single", false, "Run a single backup (for use in Kubernetes jobs)")
	flag.StringVar(&logLevel, "log-level", "info", "Log level (debug, info, warn, error)")
}

func initializeClients() error {
	// Initialize AWS configuration
	log.Println("Initializing AWS configuration...")
	var err error
	awsConfig, err = awsconfig.LoadDefaultConfig(context.TODO(), awsconfig.WithRegion(awsRegion))
	if err != nil {
		return fmt.Errorf("unable to load AWS SDK config: %v", err)
	}
	log.Println("AWS configuration loaded successfully")

	// Initialize Secrets Manager client
	log.Println("Initializing Secrets Manager client...")
	smClient = secretsmanager.NewFromConfig(awsConfig)
	log.Println("Secrets Manager client initialized")

	// Initialize S3 client
	log.Println("Initializing S3 client...")
	s3Client = s3.NewFromConfig(awsConfig)
	log.Println("S3 client initialized")

	// Initialize Kubernetes client if not in local mode and not in single mode
	if !localMode && !singleMode {
		log.Println("Initializing Kubernetes client...")
		k8sConfig, err := rest.InClusterConfig()
		if err != nil {
			return fmt.Errorf("unable to create Kubernetes config: %v", err)
		}

		k8sClient, err = kubernetes.NewForConfig(k8sConfig)
		if err != nil {
			return fmt.Errorf("unable to create Kubernetes client: %v", err)
		}
		log.Println("Kubernetes client initialized successfully")
	} else if localMode {
		log.Println("Running in local mode, skipping Kubernetes client initialization")
	}

	return nil
}

// main is the entry point for the application
func main() {
	// Set up logging to ensure it's flushed
	log.SetFlags(log.LstdFlags | log.Lmicroseconds)
	log.SetOutput(os.Stdout)

	log.Println("Starting db-restore-sanitize main execution")
	log.Printf("Command line arguments: %v", os.Args)

	// Parse flags first so we know if we're in local mode
	flag.Parse()

	// Initialize clients
	if err := initializeClients(); err != nil {
		log.Fatalf("Failed to initialize clients: %v", err)
	}

	// Initialize database service for scaling deployments if not in local mode
	var dbService *k8s.DatabaseService
	var dbServiceErr error
	if !localMode {
		dbService, dbServiceErr = k8s.NewDatabaseService()
		if dbServiceErr != nil {
			log.Printf("Warning: Failed to initialize database service: %v", dbServiceErr)
			log.Println("Database deployments will not be scaled automatically")
		} else {
			// Scale up database deployments
			log.Println("Scaling up database deployments...")
			if err := dbService.EnsureDatabasesRunning(); err != nil {
				log.Printf("Warning: Failed to scale up database deployments: %v", err)
				log.Println("Continuing with execution, but database operations may fail")
			}

			// We'll scale down databases at the end of the function instead of using defer
			// This allows us to wait for spawned jobs to complete first
		}
	}

	// Handle single mode (for Kubernetes jobs)
	if singleMode {
		log.Println("Running in single mode")

		// Check required environment variables
		requiredVars := []string{
			"DB_TYPE", "DB_HOST", "DB_PORT", "DB_NAME", "DB_USERNAME", "DB_PASSWORD",
			"S3_BUCKET", "S3_SOURCE_PATH", "S3_DESTINATION_PATH",
		}

		var missingVars []string
		for _, v := range requiredVars {
			if os.Getenv(v) == "" {
				missingVars = append(missingVars, v)
			}
		}

		if len(missingVars) > 0 {
			log.Fatalf("Missing required environment variables for single mode: %v", missingVars)
		}

		// Log if root credentials are available
		mysqlRootAvailable := os.Getenv("MYSQL_ROOT_USERNAME") != "" && os.Getenv("MYSQL_ROOT_PASSWORD") != ""
		postgresRootAvailable := os.Getenv("POSTGRES_ROOT_USERNAME") != "" && os.Getenv("POSTGRES_ROOT_PASSWORD") != ""

		if mysqlRootAvailable && postgresRootAvailable {
			log.Println("MySQL and PostgreSQL root credentials are available for database operations")
		} else if mysqlRootAvailable {
			log.Println("MySQL root credentials are available, but PostgreSQL root credentials are missing")
		} else if postgresRootAvailable {
			log.Println("PostgreSQL root credentials are available, but MySQL root credentials are missing")
		} else {
			log.Println("Root credentials not found for either database, will use regular users for database operations")
		}

		dbConfig := config.RestoreDatabaseConfig{
			DatabaseConfig: config.DatabaseConfig{
				Type:     os.Getenv("DB_TYPE"),
				Host:     os.Getenv("DB_HOST"),
				Port:     func() int { p, _ := strconv.Atoi(os.Getenv("DB_PORT")); return p }(),
				Database: os.Getenv("DB_NAME"),
				Username: os.Getenv("DB_USERNAME"),
				Password: os.Getenv("DB_PASSWORD"),
				S3Bucket: os.Getenv("S3_BUCKET"),
			},
			S3SourcePath:        os.Getenv("S3_SOURCE_PATH"),
			S3DestinationPath:   os.Getenv("S3_DESTINATION_PATH"),
			BackupFileName:      os.Getenv("BACKUP_FILE_NAME"),
			UseLatestBackup:     os.Getenv("USE_LATEST_BACKUP") == "true",
			SanitizedFilePrefix: os.Getenv("SANITIZED_FILE_PREFIX"),
		}

		log.Printf("Running single restore and sanitize for database %s", dbConfig.Database)
		if err := performRestoreAndSanitize(dbConfig); err != nil {
			log.Fatalf("Failed to restore and sanitize database %s: %v", dbConfig.Database, err)
		}
		return
	}

	// Get the ARN from command line arguments
	if flag.NArg() < 1 {
		log.Fatal("Secret ARN is required as an argument")
	}
	secretArn := flag.Arg(0)

	// Get the configuration from Secrets Manager
	log.Printf("Fetching configuration from Secrets Manager: %s", secretArn)
	secretValue, err := smClient.GetSecretValue(context.TODO(), &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretArn),
	})
	if err != nil {
		log.Fatalf("Failed to get secret value: %v", err)
	}

	var restoreConfig config.RestoreConfig
	if err := json.Unmarshal([]byte(*secretValue.SecretString), &restoreConfig); err != nil {
		log.Fatalf("Failed to parse configuration: %v", err)
	}

	log.Printf("Starting restore and sanitize for %d databases", len(restoreConfig.Databases))
	for _, dbConfig := range restoreConfig.Databases {
		// Check if the database is enabled
		if dbConfig.Enabled != nil && !*dbConfig.Enabled {
			log.Printf("Skipping disabled database %s", dbConfig.Database)
			continue
		}

		if localMode {
			// Run locally without Kubernetes
			log.Printf("Running local restore and sanitize for database %s", dbConfig.Database)
			if err := performRestoreAndSanitize(dbConfig); err != nil {
				log.Printf("Failed to restore and sanitize database %s: %v", dbConfig.Database, err)
				continue
			}
		} else {
			// Create Kubernetes job
			log.Printf("Creating Kubernetes job for database %s", dbConfig.Database)
			job, err := k8s.CreateSanitizeJob(k8sClient, dbConfig)
			if err != nil {
				log.Printf("Failed to create job for database %s: %v", dbConfig.Database, err)
				continue
			}
			log.Printf("Created sanitize job %s for database %s", job.Name, dbConfig.Database)
		}
	}
	// If we're not in local mode and we have spawned jobs, wait for them to complete
	if !localMode && dbService != nil {
		// Wait for spawned jobs to complete
		log.Println("Waiting for db-sanitize jobs to complete...")

		// Wait only for db-sanitize jobs - use a longer timeout (3 hours) for jobs that can run for over 120 minutes
		// If the hard timeout of 180 minutes is reached, the jobs will be killed and temporary databases dropped
		if err := k8s.WaitForDBSanitizeJobs(k8sClient, os.Getenv("POD_NAMESPACE"), 180*time.Minute); err != nil {
			log.Printf("Warning: Failed to wait for db-sanitize jobs: %v", err)

			// If we hit the hard timeout, the error message will indicate this
			if strings.Contains(err.Error(), "hard timeout") {
				log.Printf("Hard timeout reached. Jobs have been killed and temporary databases dropped.")
			} else {
				log.Printf("Some db-sanitize jobs may still be running. Will check again before scaling down.")
			}
		}

		// Scale down database deployments
		log.Println("Scaling down database deployments...")
		if err := dbService.ShutdownDatabases(); err != nil {
			log.Printf("Warning: Failed to scale down database deployments: %v", err)
		}
	}

	log.Println("db-restore-sanitize execution completed")
}

// performRestoreAndSanitize handles the entire process of restoring and sanitizing a database
func performRestoreAndSanitize(dbConfig config.RestoreDatabaseConfig) error {
	// Make a copy of the dbConfig to pass by reference
	dbConfigCopy := dbConfig

	// Check database connection before downloading the backup file
	if dbConfigCopy.Type == "mysql" {
		log.Printf("Checking MySQL connection before proceeding with restore and sanitize")
		if err := db.CheckMySQLConnection(dbConfigCopy); err != nil {
			return fmt.Errorf("MySQL connection check failed: %v", err)
		}
	} else if dbConfigCopy.Type == "postgresql" {
		log.Printf("Checking PostgreSQL connection before proceeding with restore and sanitize")
		if err := db.CheckPostgresConnection(dbConfigCopy); err != nil {
			return fmt.Errorf("PostgreSQL connection check failed: %v", err)
		}
	}

	// Download the backup file from S3
	backupFile, err := mnemeS3.DownloadLatestBackup(s3Client, &dbConfigCopy)
	if err != nil {
		return fmt.Errorf("failed to download backup: %v", err)
	}
	defer os.Remove(backupFile) // Clean up the backup file when done

	// Restore the database using the updated database name from the filename
	if dbConfigCopy.Type == "postgresql" {
		// PostgresRestore now handles errors internally and continues even with warnings
		// It will only return an error for critical failures
		err = db.PostgresRestore(dbConfigCopy, backupFile)
		if err != nil {
			return fmt.Errorf("failed to restore PostgreSQL database: %v", err)
		}
	} else if dbConfigCopy.Type == "mysql" {
		err = db.MySQLRestore(dbConfigCopy, backupFile)
		if err != nil {
			return fmt.Errorf("failed to restore MySQL database: %v", err)
		}
	} else {
		return fmt.Errorf("unsupported database type: %s", dbConfigCopy.Type)
	}

	// Sanitize the database
	if err := sanitizeDatabase(dbConfigCopy); err != nil {
		// Attempt to drop the database before returning the error
		log.Printf("Sanitization failed for database %s, attempting to drop the database", dbConfigCopy.Database)
		if dropErr := dropDatabase(dbConfigCopy); dropErr != nil {
			log.Printf("Warning: Failed to drop database %s after sanitization failure: %v", dbConfigCopy.Database, dropErr)
		} else {
			log.Printf("Successfully dropped database %s after sanitization failure", dbConfigCopy.Database)
		}
		return fmt.Errorf("failed to sanitize database: %v", err)
	}

	// Create a sanitized dump
	var sanitizedDumpFile string
	if dbConfigCopy.Type == "postgresql" {
		sanitizedDumpFile, err = db.PostgresDump(dbConfigCopy)
	} else if dbConfigCopy.Type == "mysql" {
		sanitizedDumpFile, err = db.MySQLDump(dbConfigCopy)
	} else {
		return fmt.Errorf("unsupported database type: %s", dbConfigCopy.Type)
	}

	if err != nil {
		// Attempt to drop the database before returning the error
		log.Printf("Failed to create sanitized dump for database %s, attempting to drop the database", dbConfigCopy.Database)
		if dropErr := dropDatabase(dbConfigCopy); dropErr != nil {
			log.Printf("Warning: Failed to drop database %s after dump creation failure: %v", dbConfigCopy.Database, dropErr)
		} else {
			log.Printf("Successfully dropped database %s after dump creation failure", dbConfigCopy.Database)
		}
		return fmt.Errorf("failed to create sanitized dump: %v", err)
	}
	defer os.Remove(sanitizedDumpFile) // Clean up the sanitized dump file when done

	// Upload the sanitized dump to S3
	if err := mnemeS3.UploadSanitizedDump(s3Client, dbConfigCopy, sanitizedDumpFile); err != nil {
		// Attempt to drop the database before returning the error
		log.Printf("Failed to upload sanitized dump for database %s, attempting to drop the database", dbConfigCopy.Database)
		if dropErr := dropDatabase(dbConfigCopy); dropErr != nil {
			log.Printf("Warning: Failed to drop database %s after upload failure: %v", dbConfigCopy.Database, dropErr)
		} else {
			log.Printf("Successfully dropped database %s after upload failure", dbConfigCopy.Database)
		}
		return fmt.Errorf("failed to upload sanitized dump: %v", err)
	}

	// Drop the temporary database after successful sanitization and upload
	if err := dropDatabase(dbConfigCopy); err != nil {
		log.Printf("Warning: Failed to drop temporary database %s: %v", dbConfigCopy.Database, err)
		// We don't return an error here as the main operation (sanitization and upload) was successful
	}

	log.Printf("Successfully restored, sanitized, and uploaded database %s", dbConfigCopy.Database)
	return nil
}

// sanitizeDatabase sanitizes a database
func sanitizeDatabase(dbConfig config.RestoreDatabaseConfig) error {
	log.Printf("Sanitizing database %s", dbConfig.Database)

	if dbConfig.Type == "postgresql" {
		// Always use the configuration-based constructor
		sanitizer := sanitize.NewPostgresSanitizer(
			dbConfig.Host,
			dbConfig.Port,
			dbConfig.Database,
			dbConfig.Username,
			dbConfig.Password,
			&dbConfig, // Pass the full config for sanitization job configuration
		)
		// The K8sClient is initialized in the NewPostgresSanitizer function
		return sanitizer.Sanitize()
	} else if dbConfig.Type == "mysql" {
		sanitizer := sanitize.NewMySQLSanitizer(
			dbConfig.Host,
			dbConfig.Port,
			dbConfig.Database,
			dbConfig.Username,
			dbConfig.Password,
		)
		return sanitizer.Sanitize()
	}

	return fmt.Errorf("unsupported database type: %s", dbConfig.Type)
}

// dropDatabase drops the temporary database after sanitization and upload
func dropDatabase(dbConfig config.RestoreDatabaseConfig) error {
	log.Printf("Dropping temporary database %s after sanitization and upload", dbConfig.Database)

	if dbConfig.Type == "postgresql" {
		// Try to use root user for database dropping if available
		rootUsername := os.Getenv("POSTGRES_ROOT_USERNAME")
		rootPassword := os.Getenv("POSTGRES_ROOT_PASSWORD")

		// If root credentials are not provided, fall back to the regular user
		username := dbConfig.Username
		password := dbConfig.Password

		if rootUsername != "" && rootPassword != "" {
			log.Printf("Using root credentials to drop database %s", dbConfig.Database)
			username = rootUsername
			password = rootPassword
		} else {
			log.Printf("Using regular credentials to drop database %s", dbConfig.Database)
		}

		// Create the drop database command for PostgreSQL
		dropCmd := exec.Command("psql",
			"-h", dbConfig.Host,
			"-p", fmt.Sprintf("%d", dbConfig.Port),
			"-U", username,
			"-d", "postgres", // Connect to the default postgres database for admin operations
			"-c", fmt.Sprintf("DROP DATABASE IF EXISTS \"%s\"", dbConfig.Database))
		dropCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", password))
		dropCmd.Stdout = os.Stdout
		dropCmd.Stderr = os.Stderr

		if err := dropCmd.Run(); err != nil {
			return fmt.Errorf("failed to drop PostgreSQL database: %v", err)
		}
		log.Printf("Successfully dropped PostgreSQL database %s", dbConfig.Database)
	} else if dbConfig.Type == "mysql" {
		// Create the drop database command for MySQL
		dropCmd := exec.Command("mysql",
			"-h", dbConfig.Host,
			"-P", fmt.Sprintf("%d", dbConfig.Port),
			"-u", dbConfig.Username,
			fmt.Sprintf("-p%s", dbConfig.Password),
			"-e", fmt.Sprintf("DROP DATABASE IF EXISTS `%s`", dbConfig.Database))
		dropCmd.Stdout = os.Stdout
		dropCmd.Stderr = os.Stderr

		if err := dropCmd.Run(); err != nil {
			return fmt.Errorf("failed to drop MySQL database: %v", err)
		}
		log.Printf("Successfully dropped MySQL database %s", dbConfig.Database)
	} else {
		return fmt.Errorf("unsupported database type: %s", dbConfig.Type)
	}

	return nil
}
